"""
Experiment with Small Language Models (SLMs) for Agentic AI
Demonstrates SLM usage for text summarization.
"""

import logging
import torch
from datasets import load_dataset
from transformers import AutoModelForCausalLM, AutoTokenizer, BitsAndBytesConfig, TrainingArguments, Trainer
from peft import LoraConfig, get_peft_model
from rouge_score import rouge_scorer
import time

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def load_model_and_tokenizer(model_name="microsoft/phi-3-mini-4k-instruct"):
    """Load the model and tokenizer."""
    logger.info(f"Loading model '{model_name}'...")
    tokenizer = AutoTokenizer.from_pretrained(model_name)
    
    # Load model with 4-bit quantization
    quantization_config = BitsAndBytesConfig(load_in_4bit=True)
    model = AutoModelForCausalLM.from_pretrained(
        model_name,
        quantization_config=quantization_config,
        device_map="auto"
    )
    
    return tokenizer, model


def load_and_prepare_dataset():
    """Load and prepare the CNN/Daily Mail dataset."""
    logger.info("Loading CNN/Daily Mail dataset...")
    dataset = load_dataset("cnn_dailymail", '1.0.0')
    return dataset


def preprocess_data(dataset, tokenizer):
    """Preprocess the dataset for training."""
    def preprocess(examples):
        max_input_length = 512
        
        # Format inputs for causal language modeling
        input_texts = []
        for i in range(len(examples["article"])):
            article = examples["article"][i]
            highlights = examples["highlights"][i]
            input_text = f"Summarize: {article} Summary: {highlights}"
            input_texts.append(input_text)
        
        # Tokenize inputs
        model_inputs = tokenizer(
            input_texts,
            padding="max_length",
            truncation=True,
            max_length=max_input_length,
            return_tensors="pt"
        )
        
        # Create labels for causal language modeling
        labels = model_inputs["input_ids"].clone()
        
        # Find position of "Summary:" token to mask input part
        summary_token_id = tokenizer.encode("Summary:", add_special_tokens=False)[0]
        
        # Process each example in batch
        for i in range(labels.shape[0]):
            summary_positions = (labels[i] == summary_token_id).nonzero(as_tuple=True)[0]
            if len(summary_positions) > 0:
                labels[i, :summary_positions[0]] = -100
        
        # Replace padding tokens with -100
        labels[labels == tokenizer.pad_token_id] = -100
        
        return {
            "input_ids": model_inputs["input_ids"],
            "attention_mask": model_inputs["attention_mask"],
            "labels": labels
        }
    
    processed_dataset = dataset.map(preprocess, batched=True)
    processed_dataset.set_format(type="torch", columns=["input_ids", "attention_mask", "labels"])
    return processed_dataset


def generate_summary(model, tokenizer, input_text):
    """Generate a summary for the input text."""
    start_time = time.time()
    inputs = tokenizer(input_text, return_tensors="pt")
    
    # Move inputs to GPU if CUDA is available
    if torch.cuda.is_available():
        inputs = {k: v.to("cuda") for k, v in inputs.items()}
    
    execution_time = time.time() - start_time
    print(f"tokenizer time: {execution_time:.4f} seconds")
    outputs = model.generate(**inputs, max_length=150)
    execution_time = time.time() - start_time
    print(f"Model Generation time: {execution_time:.4f} seconds")
    summary = tokenizer.decode(outputs[0], skip_special_tokens=True)
    execution_time = time.time() - start_time
    print(f"Decode time: {execution_time:.4f} seconds")
    return summary

def evaluate_summary(summary, reference_summary):
    """Evaluate the generated summary using ROUGE metrics."""
    scorer = rouge_scorer.RougeScorer(['rouge1', 'rouge2', 'rougeL'], use_stemmer=True)
    scores = scorer.score(summary, reference_summary)
    
    for key, value in scores.items():
        logger.info(f"{key}: Precision: {value.precision:.4f}, Recall: {value.recall:.4f}, F1: {value.fmeasure:.4f}")
    
    return scores


def apply_lora(model):
    """Apply QLoRA to the model."""
    lora_config = LoraConfig(
        r=8,
        lora_alpha=32,
        target_modules=["qkv_proj", "o_proj", "gate_up_proj", "down_proj"],
        lora_dropout=0.05,
        bias="none"
    )
    model = get_peft_model(model, lora_config)
    return model


def setup_training(model, dataset, output_dir="./slm_summarization"):
    """Set up training configuration and trainer."""
    training_args = TrainingArguments(
        output_dir=output_dir,
        per_device_train_batch_size=4,
        num_train_epochs=3,
        save_steps=500,
        save_total_limit=2,
        eval_strategy="steps",
        eval_steps=500,
        logging_steps=100,
        learning_rate=2e-5,
        fp16=True,
    )
    
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=dataset["train"],
        eval_dataset=dataset["validation"]
    )
    return trainer


def main():
    """Run the SLM experiment."""
    model_name = "microsoft/phi-3-mini-4k-instruct"
    tokenizer, model = load_model_and_tokenizer(model_name)
    
    model = apply_lora(model)
    
    dataset = load_and_prepare_dataset()
    dataset = preprocess_data(dataset, tokenizer)
    
    # Generate and evaluate summary
    input_text = "The Yamuna river in India's capital, New Delhi, surged past the official danger mark on Tuesday morning following days of torrential monsoon rains in the northern states of Uttarakhand and Himachal Pradesh. The water level at the Old Railway Bridge was recorded at 205.88 meters, exceeding the danger level of 205.33 meters."
    summary = generate_summary(model, tokenizer, input_text)
    logger.info(f"Generated summary: {summary}")
    
    reference_summary = "The Yamuna river in Delhi has risen above the danger mark (205.33 m), reaching 205.88 m at the Old Railway Bridge after heavy rains in Uttarakhand and Himachal Pradesh. This raises risks of flooding in low-lying areas, evacuations, traffic disruptions, and impact on water supply."
    evaluate_summary(summary, reference_summary)
    
    # Set up and start training
    trainer = setup_training(model, dataset)
    try:
        trainer.train()
    except Exception as e:
        logger.error(f"Training failed: {str(e)}")
        raise


if __name__ == "__main__":
    main()