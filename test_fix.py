# -*- coding: utf-8 -*-
"""
Test script to verify the fix for batch size mismatch issue.
"""

import logging
from experiment_with_slms_for_agentic_ai import load_model_and_tokenizer, load_and_prepare_dataset, preprocess_data, apply_lora, setup_training

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_training_setup():
    """Test the training setup to verify the fix."""
    try:
        model_name = "microsoft/phi-3-mini-4k-instruct"
        tokenizer, model = load_model_and_tokenizer(model_name)
        
        model = apply_lora(model)
        
        dataset = load_and_prepare_dataset()
        dataset = preprocess_data(dataset, tokenizer)
        
        trainer = setup_training(model, dataset, "./slm_summarization")
        logger.info("Training setup completed successfully.")
        
        logger.info("Testing a single training step...")
        trainer.train(resume_from_checkpoint=False)
        logger.info("Single training step completed successfully.")
        
    except Exception as e:
        logger.error(f"Error during test: {str(e)}")
        raise

if __name__ == "__main__":
    test_training_setup()