"""
Experiment with Qwen2-0.5B Model for Agentic AI
Demonstrates Qwen2-0.5B usage for text summarization and question answering.
Can load either the original pretrained model or a previously trained model.
"""

import logging
import torch
from datasets import load_dataset
from transformers import AutoModelForCausalLM, AutoTokenizer, BitsAndBytesConfig
from rouge_score import rouge_scorer
import time
import json
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def load_qwen2_model_and_tokenizer(model_name="Qwen/Qwen2-0.5B", use_trained_model=False, trained_model_path="./qwen2_summarization"):
    """Load the Qwen2-0.5B model and tokenizer.
    
    Args:
        model_name: Name of the pretrained model to load
        use_trained_model: Whether to load a previously trained model
        trained_model_path: Path to the trained model directory
    """
    if use_trained_model:
        logger.info(f"Loading trained Qwen2 model from '{trained_model_path}'...")
        # Load tokenizer from trained model
        tokenizer = AutoTokenizer.from_pretrained(trained_model_path)
        
        # Load model with 4-bit quantization for efficiency
        quantization_config = BitsAndBytesConfig(
            load_in_4bit=True,
            bnb_4bit_compute_dtype=torch.float16,
            bnb_4bit_use_double_quant=True,
            bnb_4bit_quant_type="nf4"
        )
        
        model = AutoModelForCausalLM.from_pretrained(
            trained_model_path,
            quantization_config=quantization_config,
            device_map="auto",
            torch_dtype=torch.float16,
            trust_remote_code=True
        )
    else:
        logger.info(f"Loading Qwen2 pretrained model '{model_name}'...")
        
        # Load tokenizer
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        
        # Set pad token if not present
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
        
        # Load model with 4-bit quantization for efficiency
        quantization_config = BitsAndBytesConfig(
            load_in_4bit=True,
            bnb_4bit_compute_dtype=torch.float16,
            bnb_4bit_use_double_quant=True,
            bnb_4bit_quant_type="nf4"
        )
        
        model = AutoModelForCausalLM.from_pretrained(
            model_name,
            quantization_config=quantization_config,
            device_map="auto",
            torch_dtype=torch.float16,
            trust_remote_code=True
        )
    
    return tokenizer, model




def preprocess_qwen2_data(dataset, tokenizer):
    """Preprocess the dataset for Qwen2 training."""
    def preprocess(examples):
        max_input_length = 1024  # Qwen2-0.5B can handle longer sequences
        
        # Format inputs using Qwen2's chat format
        input_texts = []
        for i in range(len(examples["article"])):
            article = examples["article"][i]
            highlights = examples["highlights"][i]
            
            # Use Qwen2's preferred format
            input_text = f"<|im_start|>system\nYou are a helpful assistant that summarizes news articles.<|im_end|>\n<|im_start|>user\nSummarize the following article:\n{article}<|im_end|>\n<|im_start|>assistant\n{highlights}<|im_end|>"
            input_texts.append(input_text)
        
        # Tokenize inputs
        model_inputs = tokenizer(
            input_texts,
            padding="max_length",
            truncation=True,
            max_length=max_input_length,
            return_tensors="pt"
        )
        
        # Create labels for causal language modeling
        labels = model_inputs["input_ids"].clone()
        
        # Find position of assistant token to mask input part
        assistant_token = "<|im_start|>assistant"
        
        # Process each example in batch
        for i in range(labels.shape[0]):
            input_text = input_texts[i]
            assistant_pos = input_text.find(assistant_token)
            if assistant_pos != -1:
                # Tokenize up to assistant position to find where to start labels
                prefix_tokens = tokenizer.encode(input_text[:assistant_pos], add_special_tokens=False)
                if len(prefix_tokens) < labels.shape[1]:
                    labels[i, :len(prefix_tokens)] = -100
        
        # Replace padding tokens with -100
        labels[labels == tokenizer.pad_token_id] = -100
        
        return {
            "input_ids": model_inputs["input_ids"],
            "attention_mask": model_inputs["attention_mask"],
            "labels": labels
        }
    
    processed_dataset = dataset.map(preprocess, batched=True)
    processed_dataset.set_format(type="torch", columns=["input_ids", "attention_mask", "labels"])
    return processed_dataset


def generate_qwen2_summary(model, tokenizer, input_text):
    """Generate a summary using Qwen2 model."""
    start_time = time.time()
    
    # Format input using Qwen2's chat format
    formatted_input = f"<|im_start|>system\nYou are a helpful assistant that summarizes news articles.<|im_end|>\n<|im_start|>user\nSummarize the following article:\n{input_text}<|im_end|>\n<|im_start|>assistant\n"
    
    inputs = tokenizer(formatted_input, return_tensors="pt")
    
    # Move inputs to GPU if CUDA is available
    if torch.cuda.is_available():
        inputs = {k: v.to("cuda") for k, v in inputs.items()}
    
    tokenizer_time = time.time() - start_time
    logger.info(f"Tokenizer time: {tokenizer_time:.4f} seconds")
    
    # Generate with appropriate parameters for Qwen2
    generation_start = time.time()
    with torch.no_grad():
        outputs = model.generate(
            **inputs,
            max_new_tokens=150,
            temperature=0.7,
            do_sample=True,
            top_p=0.9,
            pad_token_id=tokenizer.eos_token_id,
            eos_token_id=tokenizer.eos_token_id
        )
    
    generation_time = time.time() - generation_start
    logger.info(f"Model generation time: {generation_time:.4f} seconds")
    
    # Decode only the new tokens (summary)
    input_length = inputs["input_ids"].shape[1]
    summary_tokens = outputs[0][input_length:]
    summary = tokenizer.decode(summary_tokens, skip_special_tokens=True)
    
    decode_time = time.time() - generation_start - generation_time
    logger.info(f"Decode time: {decode_time:.4f} seconds")
    
    total_time = time.time() - start_time
    logger.info(f"Total generation time: {total_time:.4f} seconds")
    
    return summary.strip()



def evaluate_summary(summary, reference_summary, prefix=""):
    """Evaluate the generated summary using ROUGE metrics."""
    scorer = rouge_scorer.RougeScorer(['rouge1', 'rouge2', 'rougeL'], use_stemmer=True)
    scores = scorer.score(summary, reference_summary)

    logger.info(f"{prefix}ROUGE Evaluation Results:")
    for key, value in scores.items():
        logger.info(f"{prefix}{key}: Precision: {value.precision:.4f}, Recall: {value.recall:.4f}, F1: {value.fmeasure:.4f}")

    return scores


def compare_rouge_scores(pre_scores, post_scores):
    """Compare ROUGE scores before and after training."""
    logger.info("\n" + "="*60)
    logger.info("ROUGE SCORE COMPARISON (Before vs After Training)")
    logger.info("="*60)

    improvements = {}
    for metric in ['rouge1', 'rouge2', 'rougeL']:
        pre_f1 = pre_scores[metric].fmeasure
        post_f1 = post_scores[metric].fmeasure
        improvement = post_f1 - pre_f1
        improvements[metric] = improvement

        logger.info(f"{metric.upper()}:")
        logger.info(f"  Before Training F1: {pre_f1:.4f}")
        logger.info(f"  After Training F1:  {post_f1:.4f}")
        logger.info(f"  Improvement:        {improvement:+.4f} ({improvement/pre_f1*100:+.2f}%)")
        logger.info("")

    # Overall assessment
    avg_improvement = sum(improvements.values()) / len(improvements)
    logger.info(f"Average F1 Improvement: {avg_improvement:+.4f}")

    if avg_improvement > 0:
        logger.info("✅ Training improved the model's summarization performance!")
    elif avg_improvement < -0.01:
        logger.info("❌ Training decreased the model's performance significantly.")
    else:
        logger.info("➖ Training had minimal impact on performance.")

    logger.info("="*60)
    return improvements


def evaluate_on_test_samples(model, tokenizer, num_samples=5):
    """Evaluate model on multiple test samples for more robust comparison."""
    logger.info(f"\nEvaluating on {num_samples} test samples...")

    # Load test samples
    test_dataset = load_dataset("cnn_dailymail", '3.0.0', split="test[:50]")

    all_scores = {'rouge1': [], 'rouge2': [], 'rougeL': []}

    for i in range(min(num_samples, len(test_dataset))):
        article = test_dataset[i]['article']
        reference = test_dataset[i]['highlights']

        # Generate summary
        generated_summary = generate_qwen2_summary(model, tokenizer, article)

        # Evaluate
        scores = evaluate_summary(generated_summary, reference, prefix=f"Sample {i+1}: ")

        # Collect scores
        for metric in all_scores:
            all_scores[metric].append(scores[metric].fmeasure)

    # Calculate averages
    avg_scores = {}
    for metric in all_scores:
        avg_scores[metric] = sum(all_scores[metric]) / len(all_scores[metric])
        logger.info(f"Average {metric.upper()} F1: {avg_scores[metric]:.4f}")

    return avg_scores


def save_comparison_results(pre_scores, post_scores, pre_avg_scores, post_avg_scores,
                          pre_summary, post_summary, input_text, reference_summary):
    """Save comparison results to a JSON file for tracking."""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"qwen2_training_comparison_{timestamp}.json"

    # Convert rouge_scorer results to serializable format
    def scores_to_dict(scores):
        if hasattr(scores, 'items'):  # rouge_scorer format
            # Check if scores are rouge scorer objects or just a dict of floats
            sample_item = next(iter(scores.items()))[1] if scores else None
            if hasattr(sample_item, 'precision'):
                # rouge_scorer format with precision, recall, fmeasure
                return {metric: {'precision': score.precision, 'recall': score.recall, 'fmeasure': score.fmeasure}
                       for metric, score in scores.items()}
            else:
                # average scores format (dict of floats)
                return {metric: {'fmeasure': score} for metric, score in scores.items()}
        else:
            # If it's not a dict-like object, return as is
            return scores

    results = {
        'timestamp': timestamp,
        'model_name': "Qwen/Qwen2-0.5B",
        'input_text': input_text,
        'reference_summary': reference_summary,
        'before_training': {
            'summary': pre_summary,
            'single_sample_scores': scores_to_dict(pre_scores),
            'average_scores': scores_to_dict(pre_avg_scores)
        },
        'after_training': {
            'summary': post_summary,
            'single_sample_scores': scores_to_dict(post_scores),
            'average_scores': scores_to_dict(post_avg_scores)
        },
        'improvements': {
            'single_sample': {
                metric: post_scores[metric].fmeasure - pre_scores[metric].fmeasure
                for metric in ['rouge1', 'rouge2', 'rougeL']
            },
            'average': {
                metric: post_avg_scores[metric] - pre_avg_scores[metric]
                for metric in ['rouge1', 'rouge2', 'rougeL']
            }
        }
    }

    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)

    logger.info(f"Comparison results saved to: {filename}")
    return filename




def main():
    """Run the Qwen2-0.5B experiment with before/after training comparison.
    
    Can compare the original pretrained model with a previously trained model.
    """
    model_name = "Qwen/Qwen2-0.5B"
    
    # Test data for consistent comparison
    input_text = "The Yamuna river in India's capital, New Delhi, surged past the official danger mark on Tuesday morning following days of torrential monsoon rains in the northern states of Uttarakhand and Himachal Pradesh. The water level at the Old Railway Bridge was recorded at 205.88 meters, exceeding the danger level of 205.33 meters. Authorities have issued flood warnings and are monitoring the situation closely."
    reference_summary = "The Yamuna river in Delhi has risen above the danger mark (205.33 m), reaching 205.88 m at the Old Railway Bridge after heavy rains in Uttarakhand and Himachal Pradesh."
    
    # ===== EVALUATE PRETRAINED MODEL =====
    logger.info("\n" + "="*60)
    logger.info("EVALUATING PRETRAINED MODEL")
    logger.info("="*60)
    
    # Load pretrained model
    tokenizer, pretrained_model = load_qwen2_model_and_tokenizer(model_name, use_trained_model=False)
    
    # Single sample evaluation
    logger.info("Single Sample Evaluation (Pretrained Model):")
    pretrained_summary = generate_qwen2_summary(pretrained_model, tokenizer, input_text)
    logger.info(f"Generated summary (Pretrained): {pretrained_summary}")
    pretrained_scores = evaluate_summary(pretrained_summary, reference_summary, prefix="Pretrained Model - ")
    
    # Multiple samples evaluation for more robust comparison
    logger.info("\nMultiple Samples Evaluation (Pretrained Model):")
    pretrained_avg_scores = evaluate_on_test_samples(pretrained_model, tokenizer, num_samples=3)
    
    # ===== EVALUATE TRAINED MODEL =====
    logger.info("\n" + "="*60)
    logger.info("EVALUATING TRAINED MODEL")
    logger.info("="*60)
    
    # Load trained model (if available)
    try:
        tokenizer, trained_model = load_qwen2_model_and_tokenizer(model_name, use_trained_model=True)
        
        # Single sample evaluation
        logger.info("Single Sample Evaluation (Trained Model):")
        trained_summary = generate_qwen2_summary(trained_model, tokenizer, input_text)
        logger.info(f"Generated summary (Trained): {trained_summary}")
        trained_scores = evaluate_summary(trained_summary, reference_summary, prefix="Trained Model - ")
        
        # Multiple samples evaluation
        logger.info("\nMultiple Samples Evaluation (Trained Model):")
        trained_avg_scores = evaluate_on_test_samples(trained_model, tokenizer, num_samples=3)
        
        # ===== COMPARISON =====
        logger.info("\n" + "="*60)
        logger.info("DETAILED COMPARISON")
        logger.info("="*60)
        
        # Compare single sample scores
        logger.info("Single Sample Comparison:")
        compare_rouge_scores(pretrained_scores, trained_scores)
        
        # Compare average scores
        logger.info("\nMultiple Samples Average Comparison:")
        # Convert average scores to rouge_scorer format for comparison
        from collections import namedtuple
        Score = namedtuple('Score', ['precision', 'recall', 'fmeasure'])
        
        pretrained_avg_formatted = {}
        trained_avg_formatted = {}
        for metric in ['rouge1', 'rouge2', 'rougeL']:
            pretrained_avg_formatted[metric] = Score(0, 0, pretrained_avg_scores[metric])
            trained_avg_formatted[metric] = Score(0, 0, trained_avg_scores[metric])
        
        compare_rouge_scores(pretrained_avg_formatted, trained_avg_formatted)
        
        # ===== SAVE RESULTS =====
        logger.info("\nSaving comparison results...")
        save_comparison_results(
            pretrained_scores, trained_scores,
            pretrained_avg_scores, trained_avg_scores,
            pretrained_summary, trained_summary,
            input_text, reference_summary
        )
    except Exception as e:
        logger.warning(f"Could not load trained model: {str(e)}")
        logger.info("Only evaluating pretrained model.")
        
        # Save results for pretrained model only
        logger.info("\nSaving pretrained model results...")
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"qwen2_pretrained_evaluation_{timestamp}.json"
        
        # Convert rouge_scorer results to serializable format
        def scores_to_dict(scores):
            if hasattr(scores, 'items'):  # rouge_scorer format
                # Check if scores are rouge scorer objects or just a dict of floats
                sample_item = next(iter(scores.items()))[1] if scores else None
                if hasattr(sample_item, 'precision'):
                    # rouge_scorer format with precision, recall, fmeasure
                    return {metric: {'precision': score.precision, 'recall': score.recall, 'fmeasure': score.fmeasure}
                           for metric, score in scores.items()}
                else:
                    # average scores format (dict of floats)
                    return {metric: {'fmeasure': score} for metric, score in scores.items()}
            else:
                # If it's not a dict-like object, return as is
                return scores
        
        results = {
            'timestamp': timestamp,
            'model_name': "Qwen/Qwen2-0.5B",
            'input_text': input_text,
            'reference_summary': reference_summary,
            'pretrained_model': {
                'summary': pretrained_summary,
                'single_sample_scores': scores_to_dict(pretrained_scores),
                'average_scores': scores_to_dict(pretrained_avg_scores)
            }
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Pretrained model results saved to: {filename}")
    
    logger.info("\n" + "="*60)
    logger.info("EXPERIMENT COMPLETED SUCCESSFULLY!")
    logger.info("="*60)
    


if __name__ == "__main__":
    main()
