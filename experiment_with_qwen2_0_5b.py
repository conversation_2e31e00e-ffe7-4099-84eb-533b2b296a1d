"""
Simple Qwen2-0.5B Model Evaluation Script
Evaluates and compares pretrained vs trained model performance.
"""

import torch
from transformers import AutoModelForCausalLM, AutoTokenizer, BitsAndBytesConfig
from rouge_score import rouge_scorer
import json


def load_model(model_path="Qwen/Qwen2-0.5B"):
    """Load model and tokenizer with quantization."""
    print(f"Loading model: {model_path}")

    tokenizer = AutoTokenizer.from_pretrained(model_path)
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token

    quantization_config = BitsAndBytesConfig(
        load_in_4bit=True,
        bnb_4bit_compute_dtype=torch.float16,
        bnb_4bit_use_double_quant=True,
        bnb_4bit_quant_type="nf4"
    )

    model = AutoModelForCausalLM.from_pretrained(
        model_path,
        quantization_config=quantization_config,
        device_map="auto",
        torch_dtype=torch.float16,
        trust_remote_code=True
    )

    return tokenizer, model


def generate_summary(model, tokenizer, text):
    """Generate a summary using the model."""
    prompt = f"<|im_start|>system\nYou are a helpful assistant that summarizes news articles.<|im_end|>\n<|im_start|>user\nSummarize the following article:\n{text}<|im_end|>\n<|im_start|>assistant\n"

    inputs = tokenizer(prompt, return_tensors="pt")
    if torch.cuda.is_available():
        inputs = {k: v.to("cuda") for k, v in inputs.items()}

    with torch.no_grad():
        outputs = model.generate(
            **inputs,
            max_new_tokens=150,
            temperature=0.7,
            do_sample=True,
            top_p=0.9,
            pad_token_id=tokenizer.eos_token_id
        )

    # Decode only the new tokens
    input_length = inputs["input_ids"].shape[1]
    summary = tokenizer.decode(outputs[0][input_length:], skip_special_tokens=True)
    return summary.strip()


def evaluate_summary(summary, reference):
    """Calculate ROUGE scores."""
    scorer = rouge_scorer.RougeScorer(['rouge1', 'rouge2', 'rougeL'], use_stemmer=True)
    scores = scorer.score(summary, reference)

    print(f"ROUGE-1 F1: {scores['rouge1'].fmeasure:.4f}")
    print(f"ROUGE-2 F1: {scores['rouge2'].fmeasure:.4f}")
    print(f"ROUGE-L F1: {scores['rougeL'].fmeasure:.4f}")

    return scores


def compare_models(pretrained_scores, trained_scores):
    """Compare model performance."""
    print("\n" + "="*50)
    print("MODEL COMPARISON")
    print("="*50)

    for metric in ['rouge1', 'rouge2', 'rougeL']:
        pre_f1 = pretrained_scores[metric].fmeasure
        post_f1 = trained_scores[metric].fmeasure
        improvement = post_f1 - pre_f1

        print(f"{metric.upper()}:")
        print(f"  Pretrained: {pre_f1:.4f}")
        print(f"  Trained:    {post_f1:.4f}")
        print(f"  Change:     {improvement:+.4f}")
        print()

    return True


def save_results(pretrained_summary, trained_summary, pretrained_scores, trained_scores):
    """Save simple comparison results."""
    results = {
        'pretrained_summary': pretrained_summary,
        'trained_summary': trained_summary,
        'pretrained_rouge1': pretrained_scores['rouge1'].fmeasure,
        'trained_rouge1': trained_scores['rouge1'].fmeasure,
        'improvement': trained_scores['rouge1'].fmeasure - pretrained_scores['rouge1'].fmeasure
    }

    with open('comparison_results.json', 'w') as f:
        json.dump(results, f, indent=2)

    print("Results saved to comparison_results.json")


def main():
    """Simple model comparison."""
    # Test data
    text = "The Yamuna river in India's capital, New Delhi, surged past the official danger mark on Tuesday morning following days of torrential monsoon rains in the northern states of Uttarakhand and Himachal Pradesh. The water level at the Old Railway Bridge was recorded at 205.88 meters, exceeding the danger level of 205.33 meters. Authorities have issued flood warnings and are monitoring the situation closely."
    reference = "The Yamuna river in Delhi has risen above the danger mark (205.33 m), reaching 205.88 m at the Old Railway Bridge after heavy rains in Uttarakhand and Himachal Pradesh."

    # Load and test pretrained model
    print("Loading pretrained model...")
    tokenizer, pretrained_model = load_model("Qwen/Qwen2-0.5B")

    print("\nGenerating summary with pretrained model...")
    pretrained_summary = generate_summary(pretrained_model, tokenizer, text)
    print(f"Pretrained summary: {pretrained_summary}")

    print("\nEvaluating pretrained model:")
    pretrained_scores = evaluate_summary(pretrained_summary, reference)

    # Try to load trained model
    try:
        print("\nLoading trained model...")
        tokenizer, trained_model = load_model("./qwen2_summarization")

        print("\nGenerating summary with trained model...")
        trained_summary = generate_summary(trained_model, tokenizer, text)
        print(f"Trained summary: {trained_summary}")

        print("\nEvaluating trained model:")
        trained_scores = evaluate_summary(trained_summary, reference)

        # Compare models
        compare_models(pretrained_scores, trained_scores)

        # Save results
        save_results(pretrained_summary, trained_summary, pretrained_scores, trained_scores)

    except Exception as e:
        print(f"Could not load trained model: {e}")
        print("Only pretrained model evaluated.")

    print("\nDone!")


if __name__ == "__main__":
    main()
