"""
Training script for Qwen2-0.5B Model for Agentic AI
Handles training the Qwen2-0.5B model with LoRA fine-tuning on the CNN/Daily Mail dataset.

After training, use experiment_with_qwen2_0_5b.py to evaluate and compare model performance.
"""
import logging
import torch
from datasets import load_dataset
from transformers import AutoModelForCausalLM, AutoTokenizer, BitsAndBytesConfig, TrainingArguments, Trainer
from peft import LoraConfig, get_peft_model

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def load_qwen2_model_and_tokenizer(model_name="Qwen/Qwen2-0.5B"):
    """Load the Qwen2-0.5B model and tokenizer."""
    logger.info(f"Loading Qwen2 model '{model_name}'...")
    
    # Load tokenizer
    tokenizer = AutoTokenizer.from_pretrained(model_name)
    
    # Set pad token if not present
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    # Load model with 4-bit quantization for efficiency
    quantization_config = BitsAndBytesConfig(
        load_in_4bit=True,
        bnb_4bit_compute_dtype=torch.float16,
        bnb_4bit_use_double_quant=True,
        bnb_4bit_quant_type="nf4"
    )
    
    model = AutoModelForCausalLM.from_pretrained(
        model_name,
        quantization_config=quantization_config,
        device_map="auto",
        torch_dtype=torch.float16,
        trust_remote_code=True
    )
    
    return tokenizer, model


def load_and_prepare_dataset():
    """Load and prepare the CNN/Daily Mail dataset with train/eval split."""
    logger.info("Loading CNN/Daily Mail dataset...")
    # Load train and validation splits
    train_dataset = load_dataset("cnn_dailymail", '3.0.0', split="train[:800]")  # 800 samples for training
    eval_dataset = load_dataset("cnn_dailymail", '3.0.0', split="train[800:1000]")  # 200 samples for evaluation

    return {"train": train_dataset, "eval": eval_dataset}


def preprocess_qwen2_data(dataset, tokenizer):
    """Preprocess the dataset for Qwen2 training."""
    def preprocess(examples):
        max_input_length = 1024  # Qwen2-0.5B can handle longer sequences
        
        # Format inputs using Qwen2's chat format
        input_texts = []
        for i in range(len(examples["article"])):
            article = examples["article"][i]
            highlights = examples["highlights"][i]
            
            # Use Qwen2's preferred format
            input_text = f"\nYou are a helpful assistant that summarizes news articles.\n\nSummarize the following article:\n{article}\n\n{highlights}"
            input_texts.append(input_text)
        
        # Tokenize inputs
        model_inputs = tokenizer(
            input_texts,
            padding="max_length",
            truncation=True,
            max_length=max_input_length,
            return_tensors="pt"
        )
        
        # Create labels for causal language modeling
        labels = model_inputs["input_ids"].clone()
        
        # Find position of assistant token to mask input part
        assistant_token = "\n\n"
        
        # Process each example in batch
        for i in range(labels.shape[0]):
            input_text = input_texts[i]
            assistant_pos = input_text.find(assistant_token)
            if assistant_pos != -1:
                # Tokenize up to assistant position to find where to start labels
                prefix_tokens = tokenizer.encode(input_text[:assistant_pos], add_special_tokens=False)
                if len(prefix_tokens) < labels.shape[1]:
                    labels[i, :len(prefix_tokens)] = -100
        
        # Replace padding tokens with -100
        labels[labels == tokenizer.pad_token_id] = -100
        
        return {
            "input_ids": model_inputs["input_ids"],
            "attention_mask": model_inputs["attention_mask"],
            "labels": labels
        }
    
    processed_dataset = dataset.map(preprocess, batched=True)
    processed_dataset.set_format(type="torch", columns=["input_ids", "attention_mask", "labels"])
    return processed_dataset


def apply_qwen2_lora(model):
    """Apply LoRA to the Qwen2 model."""
    lora_config = LoraConfig(
        r=16,
        lora_alpha=32,
        target_modules=["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"],
        lora_dropout=0.1,
        bias="none",
        task_type="CAUSAL_LM"
    )
    model = get_peft_model(model, lora_config)
    return model


def setup_qwen2_training(model, train_dataset, eval_dataset, tokenizer, output_dir="./qwen2_summarization"):
    """Set up training configuration and trainer for Qwen2."""
    training_args = TrainingArguments(
        output_dir=output_dir,
        per_device_train_batch_size=2,  # Smaller batch size for 0.5B model
        per_device_eval_batch_size=2,   # Eval batch size
        gradient_accumulation_steps=4,
        num_train_epochs=2,
        save_steps=250,
        save_total_limit=2,
        eval_strategy="steps",
        eval_steps=250,
        logging_steps=50,
        learning_rate=1e-4,
        fp16=True,
        warmup_steps=100,
        weight_decay=0.01,
        dataloader_drop_last=True,
        load_best_model_at_end=True,
        metric_for_best_model="eval_loss",
        greater_is_better=False,
    )

    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=train_dataset,
        eval_dataset=eval_dataset,
        tokenizer=tokenizer
    )
    return trainer


def main():
    """Run the Qwen2-0.5B training."""
    model_name = "Qwen/Qwen2-0.5B"
    tokenizer, model = load_qwen2_model_and_tokenizer(model_name)

    # Apply LoRA
    logger.info("Applying LoRA...")
    model = apply_qwen2_lora(model)

    # Load and preprocess training data
    datasets = load_and_prepare_dataset()
    train_dataset = preprocess_qwen2_data(datasets["train"], tokenizer)
    eval_dataset = preprocess_qwen2_data(datasets["eval"], tokenizer)

    # Setup and run training
    trainer = setup_qwen2_training(model, train_dataset, eval_dataset, tokenizer)
    try:
        logger.info("Starting training...")
        trainer.train()
        logger.info("Training completed successfully!")
        
        # Save the final model
        trainer.save_model()
        tokenizer.save_pretrained("./qwen2_summarization")
        logger.info("Model and tokenizer saved successfully!")
    except Exception as e:
        logger.error(f"Training failed: {str(e)}")
        raise


if __name__ == "__main__":
    main()