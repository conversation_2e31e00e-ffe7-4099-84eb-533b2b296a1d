# Qwen2-0.5B Model Scripts

This directory contains two separate scripts for working with the Qwen2-0.5B model for text summarization:

## Scripts Overview

### 1. `train_qwen2_0_5b.py` - Training Script
**Purpose**: Handles training/fine-tuning of the Qwen2-0.5B model using LoRA (Low-Rank Adaptation).

**Features**:
- Loads the pretrained Qwen2-0.5B model
- Applies LoRA for efficient fine-tuning
- Trains on CNN/Daily Mail dataset
- Saves the trained model to `./qwen2_summarization/`

**Usage**:
```bash
python train_qwen2_0_5b.py
```

**Key Functions**:
- `load_qwen2_model_and_tokenizer()`: Loads the base model
- `preprocess_qwen2_data()`: Prepares training data
- `apply_qwen2_lora()`: Applies LoRA configuration
- `setup_qwen2_training()`: Configures training parameters

### 2. `experiment_with_qwen2_0_5b.py` - Evaluation Script
**Purpose**: Evaluates and compares model performance between pretrained and trained versions.

**Features**:
- Loads either pretrained or previously trained models
- Generates summaries for test inputs
- Evaluates using ROUGE metrics
- Compares performance before/after training
- Saves detailed comparison results to JSON files

**Usage**:
```bash
python experiment_with_qwen2_0_5b.py
```

**Key Functions**:
- `load_qwen2_model_and_tokenizer()`: Loads models (pretrained or trained)
- `generate_qwen2_summary()`: Generates summaries using the model
- `evaluate_summary()`: Calculates ROUGE scores
- `compare_rouge_scores()`: Compares performance metrics
- `evaluate_on_test_samples()`: Multi-sample evaluation
- `save_comparison_results()`: Saves results to JSON

## Workflow

1. **First, train the model** (optional):
   ```bash
   python train_qwen2_0_5b.py
   ```
   This will create a trained model in `./qwen2_summarization/`

2. **Then, evaluate and compare**:
   ```bash
   python experiment_with_qwen2_0_5b.py
   ```
   This will:
   - Evaluate the pretrained model
   - If available, evaluate the trained model
   - Compare their performance
   - Save results to a timestamped JSON file

## Output Files

- **Training**: Saves model to `./qwen2_summarization/`
- **Evaluation**: Creates JSON files with names like:
  - `qwen2_training_comparison_YYYYMMDD_HHMMSS.json` (if both models available)
  - `qwen2_pretrained_evaluation_YYYYMMDD_HHMMSS.json` (if only pretrained model)

## Requirements

Both scripts require the same dependencies listed in `requirements.txt`:
- transformers
- torch
- datasets
- rouge-score
- peft
- bitsandbytes
- accelerate

## Model Configuration

Both scripts use:
- 4-bit quantization for memory efficiency
- LoRA for training (r=16, alpha=32)
- Qwen2's chat format for prompts
- ROUGE metrics for evaluation
