{"best_global_step": null, "best_metric": null, "best_model_checkpoint": null, "epoch": 2.0, "eval_steps": 250, "global_step": 200, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.5, "grad_norm": 0.6326956748962402, "learning_rate": 4.9e-05, "loss": 2.6463, "step": 50}, {"epoch": 1.0, "grad_norm": 0.6811201572418213, "learning_rate": 9.900000000000001e-05, "loss": 2.5397, "step": 100}, {"epoch": 1.5, "grad_norm": 0.7917042970657349, "learning_rate": 5.1000000000000006e-05, "loss": 2.4956, "step": 150}, {"epoch": 2.0, "grad_norm": 0.8392866849899292, "learning_rate": 1.0000000000000002e-06, "loss": 2.4616, "step": 200}], "logging_steps": 50, "max_steps": 200, "num_input_tokens_seen": 0, "num_train_epochs": 2, "save_steps": 250, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": true}, "attributes": {}}}, "total_flos": 3604771504128000.0, "train_batch_size": 2, "trial_name": null, "trial_params": null}